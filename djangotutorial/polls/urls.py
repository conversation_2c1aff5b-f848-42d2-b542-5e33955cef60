from django.urls import path
from . import views
from django.urls import path
from rest_framework_simplejwt.views import (
    TokenObtainPairView,
    TokenRefreshView,
)


urlpatterns = [
    path("",views.index,name ="index"),
    path("second/",views.second,name ="second"),
    path("profile/", views.profile_form, name="profile_form"),
    path("profiles/", views.profile_list, name="profile_list"),
    path("profile/<int:pk>/update/", views.profile_update, name="profile_update"),
    path("profile/<int:pk>/delete/", views.profile_delete, name="profile_delete"),
    path("user/",views.getalluser),
    path('api/token/', TokenObtainPairView.as_view(), name='token_obtain_pair'),
    path('api/token/refresh/', TokenRefreshView.as_view(), name='token_refresh'),
    

]
from django.db import models


class Question(models.Model):
    # ...
    def __str__(self):
        return self.question_text


class Choice(models.Model):
    # ...
    def __str__(self):
        return self.choice_text

class RequestLog(models.Model):
    # ...
    def __str__(self):
        return self.RequestLog

class Question(models.Model):
    question_text = models.CharField(max_length=200)
    pub_date = models.DateTimeField("date published")


class Choice(models.Model):
    question = models.ForeignKey(Question, on_delete=models.CASCADE)
    choice_text = models.Char<PERSON><PERSON>(max_length=200)
    votes = models.IntegerField(default=0)

class UserProfile(models.Model):
    name = models.CharField(max_length=100)
    email = models.EmailField(max_length=100)
    age = models.IntegerField()
    bio = models.TextField(max_length=500, blank=True)
class RequestLog(models.Model):
    path =models.CharField(max_length=255)
    method = models.Char<PERSON>ield(max_length=10)
    status_code = models.IntegerField()


class Student(models.Model):
    name = models.Char<PERSON>ield(max_length=100)
    age = models.IntegerField()
    notes =models.CharField(max_length=1000)
    
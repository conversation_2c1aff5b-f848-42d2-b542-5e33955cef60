
from django.http import HttpResponse
from .forms import UserProfileForm
from .models import UserProfile
from django.http import HttpResponse,JsonResponse
from django.shortcuts import render, redirect, get_object_or_404
from django.http import HttpResponse
from .models import Question, Choice, UserProfile
from .forms import UserProfileForm
from .serializers import UserProfileSerializer

def index(request):
    return HttpResponse("hello world, you are at poll's index ")

def second(request):

    latest_questions = Question.objects.all()
    
    
    output = "<h1>Poll Questions</h1>"
    for question in latest_questions:
        output += f"<h2>{question.question_text}</h2>"
        output += "<ul>"
        
        for choice in question.choice_set.all():
            output += f"<li>{choice.choice_text} - {choice.votes} votes</li>"
        output += "</ul>"
    return HttpResponse(output)

def profile_form(request):
    print("POST data:", request.POST.dict())
    if request.method == "POST":
        form = UserProfileForm(request.POST)
        if form.is_valid():
            form.save()
            return redirect('profile_list')
    else:
        form = UserProfileForm()
    
    return render(request, 'polls/profile_form.html', {'form': form})
    
def profile_list(request):
    profiles = UserProfile.objects.all()
    return render(request, 'polls/profile_list.html', {'profiles': profiles})




def profile_update(request, pk):
    profile = get_object_or_404(UserProfile, pk=pk)
    if request.method == "POST":
        form = UserProfileForm(request.POST, instance=profile)
        if form.is_valid():
            form.save()
            return redirect('profile_list')
    else:
        form = UserProfileForm(instance=profile)
    
    return render(request, 'polls/profile_form.html', {'form': form, 'profile': profile})

def profile_delete(request, pk):
    profile = get_object_or_404(UserProfile, pk=pk)
    if request.method == "POST":
        profile.delete()
        return redirect('profile_list')
    
    return render(request, 'polls/profile_confirm_delete.html', {'profile': profile})


## rest 

def getalluser(request):
    allprofiles= UserProfile.objects.all()
    serielizer=UserProfileSerializer(allprofiles,many=True)
    return JsonResponse(serielizer.data,safe=False) 


from django.shortcuts import render
from rest_framework.response import Response
from rest_framework.views import APIView
from .models import Student
from serializers import StudentSerializer

class StudentApi(APIView):
    def get(self, request):
        students = Student.objects.all()
        serializer = StudentSerializer(students, many=True)
        return response({
            "status": True ,
            "data": serializer.data
        })